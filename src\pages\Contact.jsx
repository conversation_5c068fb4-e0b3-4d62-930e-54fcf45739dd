import { Helmet } from 'react-helmet-async'
import { useState } from 'react'

const Contact = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    message: ''
  })

  const handleSubmit = (e) => {
    e.preventDefault()
    // Handle form submission
    alert('תודה על ההודעה! נחזור אליכם בהקדם.')
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      message: ''
    })
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <>
      <Helmet>
        <title>צור קשר - סרי ווי | נהגים ומדריכים פרטיים בסרי לנקה</title>
        <meta name="description" content="צרו קשר עם סרי ווי - הנהגים והמדריכים הפרטיים המובילים בסרי לנקה. זמינים 24/7 לייעוץ ותכנון הטיול המושלם שלכם" />
      </Helmet>

      <section className="contact-hero">
        <div className="hero-content">
          <h1>צור קשר איתנו</h1>
          <p>אנחנו כאן בשבילכם 24/7 לכל שאלה, ייעוץ או תכנון הטיול המושלם שלכם בסרי לנקה</p>
        </div>
      </section>

      <section className="contact-methods">
        <div className="container">
          <h2 className="section-title">דרכי התקשרות</h2>
          <div className="methods-grid">
            <div className="method-card">
              <div className="method-icon">
                <i className="fab fa-whatsapp"></i>
              </div>
              <h3>וואטסאפ</h3>
              <p>הדרך הכי מהירה ונוחה ליצירת קשר. זמינים 24/7</p>
              <a href="https://wa.me/94771234567" className="contact-link whatsapp-link">
                <i className="fab fa-whatsapp"></i> שלח הודעה
              </a>
            </div>
            <div className="method-card">
              <div className="method-icon">
                <i className="fas fa-phone"></i>
              </div>
              <h3>טלפון</h3>
              <p>התקשרו אלינו ישירות לייעוץ מקצועי</p>
              <a href="tel:+94771234567" className="contact-link">
                <i className="fas fa-phone"></i> +94 77 123 4567
              </a>
            </div>
            <div className="method-card">
              <div className="method-icon">
                <i className="fas fa-envelope"></i>
              </div>
              <h3>אימייל</h3>
              <p>שלחו לנו פרטים על הטיול שלכם</p>
              <a href="mailto:<EMAIL>" className="contact-link">
                <i className="fas fa-envelope"></i> <EMAIL>
              </a>
            </div>
          </div>
        </div>
      </section>

      <section className="contact-form-section">
        <div className="container">
          <h2 className="section-title">שלחו לנו הודעה</h2>
          <div className="form-container">
            <form onSubmit={handleSubmit}>
              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="firstName">שם פרטי *</label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="lastName">שם משפחה *</label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
              <div className="form-grid">
                <div className="form-group">
                  <label htmlFor="email">אימייל *</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                  />
                </div>
                <div className="form-group">
                  <label htmlFor="phone">טלפון *</label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
              <div className="form-group">
                <label htmlFor="message">הודעה *</label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  placeholder="ספרו לנו על הטיול שלכם..."
                  required
                ></textarea>
              </div>
              <button type="submit" className="submit-btn">
                <i className="fas fa-paper-plane"></i> שלח הודעה
              </button>
            </form>
          </div>
        </div>
      </section>
    </>
  )
}

export default Contact
