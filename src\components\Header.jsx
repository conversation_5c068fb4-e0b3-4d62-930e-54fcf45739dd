import { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const location = useLocation()

  const isActive = (path) => {
    if (path === '/' && location.pathname === '/') return true
    if (path !== '/' && location.pathname.startsWith(path)) return true
    return false
  }

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen)
  }

  return (
    <header>
      <nav>
        <div className="logo">סרי ווי</div>
        <ul className={`nav-links ${isMobileMenuOpen ? 'mobile-open' : ''}`}>
          <li>
            <Link 
              to="/" 
              className={isActive('/') ? 'active' : ''}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              בית
            </Link>
          </li>
          <li>
            <Link 
              to="/cars" 
              className={isActive('/cars') ? 'active' : ''}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              הרכבים שלנו
            </Link>
          </li>
          <li>
            <Link 
              to="/hotels" 
              className={isActive('/hotels') ? 'active' : ''}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              מלונות
            </Link>
          </li>
          <li>
            <Link 
              to="/magazine" 
              className={isActive('/magazine') ? 'active' : ''}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              מגזין
            </Link>
          </li>
          <li>
            <Link 
              to="/faq" 
              className={isActive('/faq') ? 'active' : ''}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              שאלות נפוצות
            </Link>
          </li>
          <li>
            <Link 
              to="/contact" 
              className={isActive('/contact') ? 'active' : ''}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              צור קשר
            </Link>
          </li>
        </ul>
        <button 
          className="mobile-menu-toggle"
          onClick={toggleMobileMenu}
          aria-label="תפריט ניווט"
        >
          <i className="fas fa-bars"></i>
        </button>
      </nav>
    </header>
  )
}

export default Header
