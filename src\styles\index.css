/* Sri Way - Travel Website Styles */

:root {
    --primary-color: #1a365d;
    --secondary-color: #ff6b35;
    --accent-color: #2d5a87;
    --text-dark: #333;
    --text-light: #666;
    --bg-light: #f8f9fa;
    --success-color: #10b981;
    --error-color: #ef4444;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    direction: rtl;
    background: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Header */
header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #fff;
    text-decoration: none;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 5px;
}

.nav-links a:hover,
.nav-links a.active {
    background: rgba(255, 255, 255, 0.2);
    color: var(--secondary-color);
}

.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(26, 54, 93, 0.8), rgba(45, 90, 135, 0.8)), url('/gallery/1.jpg');
    background-size: cover;
    background-position: center;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
}

.hero-content {
    max-width: 800px;
    padding: 2rem;
}

.hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease-out;
}

.hero p {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
    animation: fadeInUp 1s ease-out 0.3s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.cta-button.primary {
    background: var(--secondary-color);
    color: white;
}

.cta-button.primary:hover {
    background: #e55a2b;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
}

.cta-button.secondary {
    background: transparent;
    color: white;
    border-color: white;
}

.cta-button.secondary:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-2px);
}

.cta-button.whatsapp {
    background: #25d366;
    color: white;
}

.cta-button.whatsapp:hover {
    background: #20b954;
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(37, 211, 102, 0.3);
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    color: var(--primary-color);
    position: relative;
}

.section-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: var(--secondary-color);
    margin: 1rem auto;
    border-radius: 2px;
}

/* Services Section */
.services {
    background: var(--bg-light);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.service-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-top: 4px solid var(--secondary-color);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.service-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
}

.service-link:hover {
    color: var(--secondary-color);
}

/* Why Choose Us */
.why-choose-us {
    background: white;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
}

.feature-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    background: var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
}

.feature-item h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.feature-item p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Destinations */
.destinations {
    background: var(--bg-light);
}

.destinations-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.destination-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.destination-card:hover {
    transform: translateY(-5px);
}

.destination-image {
    height: 250px;
    background-size: cover;
    background-position: center;
    position: relative;
    display: flex;
    align-items: flex-end;
}

.destination-overlay {
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 2rem;
    width: 100%;
}

.destination-overlay h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.destination-overlay p {
    opacity: 0.9;
}

/* Testimonials */
.testimonials {
    background: white;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.testimonial-card {
    background: var(--bg-light);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
    position: relative;
}

.testimonial-card::before {
    content: '"';
    font-size: 4rem;
    color: var(--secondary-color);
    position: absolute;
    top: -10px;
    right: 20px;
    font-family: serif;
}

.testimonial-content p {
    font-style: italic;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.testimonial-author strong {
    color: var(--primary-color);
    display: block;
    margin-bottom: 0.5rem;
}

.testimonial-author span {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    text-align: center;
}

.cta-section h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Footer */
footer {
    background: var(--text-dark);
    color: white;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: var(--secondary-color);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.footer-section p {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--secondary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: var(--secondary-color);
}

.footer-bottom {
    border-top: 1px solid #555;
    padding-top: 2rem;
    text-align: center;
    color: #ccc;
}

/* WhatsApp Button */
.whatsapp-btn {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 60px;
    height: 60px;
    background: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    text-decoration: none;
    box-shadow: 0 4px 12px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
    z-index: 1000;
}

.whatsapp-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.6);
}

/* Cars Page Styles */
.cars-hero {
    background: linear-gradient(rgba(26, 54, 93, 0.8), rgba(45, 90, 135, 0.8)), url('/gallery/2.jpg');
    background-size: cover;
    background-position: center;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-top: 80px;
}

.cars-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.cars-hero p {
    font-size: 1.3rem;
    max-width: 600px;
    margin: 0 auto;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.our-cars {
    padding: 80px 0;
    background: white;
}

.cars-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
    margin-top: 3rem;
}

.car-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.car-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.car-card.featured {
    border: 3px solid var(--secondary-color);
}

.car-image {
    height: 250px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.car-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--secondary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.car-details {
    padding: 2rem;
}

.car-details h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.car-specs {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.car-specs span {
    background: var(--bg-light);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.car-specs i {
    color: var(--secondary-color);
}

.car-details p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.car-price {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.car-price .price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--secondary-color);
}

.car-price .includes {
    font-size: 0.9rem;
    color: var(--text-light);
}

.available-fleet {
    padding: 80px 0;
    background: var(--bg-light);
}

.fleet-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.fleet-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.fleet-card:hover {
    transform: translateY(-5px);
}

.fleet-image {
    height: 200px;
    background-size: cover;
    background-position: center;
}

.fleet-info {
    padding: 1.5rem;
}

.fleet-info h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.fleet-info p {
    color: var(--text-light);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.fleet-info .capacity {
    background: var(--primary-color);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-left: 0.5rem;
}

.fleet-info .price {
    color: var(--secondary-color);
    font-weight: bold;
    font-size: 1.1rem;
}

.car-features {
    padding: 80px 0;
    background: white;
}

/* Contact Page Styles */
.contact-hero {
    background: linear-gradient(rgba(26, 54, 93, 0.8), rgba(45, 90, 135, 0.8)), url('/gallery/1.jpg');
    background-size: cover;
    background-position: center;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-top: 80px;
}

.contact-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.contact-hero p {
    font-size: 1.3rem;
    max-width: 600px;
    margin: 0 auto;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.contact-methods {
    padding: 80px 0;
    background: white;
}

.methods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.method-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s, box-shadow 0.3s;
    border-top: 4px solid var(--secondary-color);
}

.method-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.method-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.method-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.method-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.contact-link {
    background: var(--secondary-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s;
    display: inline-block;
}

.contact-link:hover {
    background: #e55a2b;
    transform: translateY(-2px);
}

.whatsapp-link {
    background: #25d366;
}

.whatsapp-link:hover {
    background: #20b954;
}

/* Contact Form */
.contact-form-section {
    padding: 80px 0;
    background: var(--bg-light);
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
    font-weight: 600;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s;
    font-family: inherit;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: 1rem 3rem;
    border: none;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s;
    width: 100%;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
}

.submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        right: 0;
        background: var(--primary-color);
        width: 100%;
        flex-direction: column;
        padding: 1rem 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .nav-links.mobile-open {
        display: flex;
    }

    .mobile-menu-toggle {
        display: block;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .section-title {
        font-size: 2rem;
    }

    .services-grid,
    .features-grid,
    .destinations-grid,
    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .cars-grid {
        grid-template-columns: 1fr;
    }

    .car-specs {
        justify-content: center;
    }

    .car-price {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .whatsapp-btn {
        bottom: 15px;
        left: 15px;
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    /* Gallery responsive */
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 0.5rem;
        padding: 0 1rem;
    }

    .lightbox-prev,
    .lightbox-next {
        display: none;
    }

    .lightbox-close {
        top: 10px;
        right: 10px;
        padding: 0.8rem;
        font-size: 1.2rem;
    }

    /* Magazine preview responsive */
    .magazine-articles {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .magazine-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .magazine-content h3 {
        font-size: 1.2rem;
    }

    .magazine-content {
        padding: 1.2rem;
    }

    /* Magazine responsive */
    .article-card.featured {
        grid-column: span 1;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .article-item {
        flex-direction: column;
    }

    .article-thumbnail {
        width: 100%;
        height: 200px;
    }

    .newsletter-form {
        flex-direction: column;
    }

    /* FAQ responsive */
    .faq-question h4 {
        font-size: 1.1rem;
    }

    .contact-box {
        padding: 2rem;
    }

    /* Hotels responsive */
    .hotels-grid {
        grid-template-columns: 1fr;
    }

    .hotel-footer {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .hotel-features {
        justify-content: center;
    }
}

/* Magazine Preview Section (Homepage) */
.magazine-preview {
    padding: 80px 0;
    background: var(--bg-light);
}

.magazine-articles {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.magazine-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.magazine-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.magazine-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.magazine-image .article-category {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.magazine-content {
    padding: 1.5rem;
}

.magazine-content h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
    line-height: 1.4;
}

.magazine-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.magazine-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    font-size: 0.85rem;
    color: var(--text-light);
}

.magazine-content .read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: bold;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.magazine-content .read-more:hover {
    color: var(--secondary-color);
}

.magazine-cta {
    text-align: center;
    margin-top: 3rem;
}

.magazine-cta .cta-button.secondary {
    background: var(--primary-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 30px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.magazine-cta .cta-button.secondary:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* Gallery Page Styles */
.gallery-section {
    padding: 100px 0 80px;
    background: white;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1rem;
    padding: 0 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.gallery-item {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
    border-radius: 10px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.02);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: white;
    font-size: 2rem;
}

/* Lightbox Styles */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 10px;
}

.lightbox-close,
.lightbox-prev,
.lightbox-next {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 1.5rem;
    padding: 1rem;
    cursor: pointer;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.lightbox-close:hover,
.lightbox-prev:hover,
.lightbox-next:hover {
    background: rgba(255, 255, 255, 0.3);
}

.lightbox-close {
    top: 20px;
    right: 20px;
}

.lightbox-prev {
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.lightbox-next {
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

/* Magazine Page Styles */
.magazine-hero {
    background: linear-gradient(rgba(26, 54, 93, 0.8), rgba(45, 90, 135, 0.8)), url('/gallery/6.jpg');
    background-size: cover;
    background-position: center;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-top: 80px;
}

.magazine-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.magazine-hero p {
    font-size: 1.3rem;
    max-width: 600px;
    margin: 0 auto;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.featured-articles {
    padding: 80px 0;
    background: white;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.article-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.article-card.featured {
    grid-column: span 2;
}

.article-image {
    height: 250px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.article-card.featured .article-image {
    height: 300px;
}

.article-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--secondary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.article-content {
    padding: 2rem;
}

.article-category {
    color: var(--primary-color);
    font-weight: bold;
    font-size: 0.9rem;
    text-transform: uppercase;
    margin-bottom: 0.5rem;
}

.article-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
    line-height: 1.4;
}

.article-content p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.article-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.read-more {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.read-more:hover {
    color: var(--secondary-color);
}

.magazine-categories {
    padding: 80px 0;
    background: var(--bg-light);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.category-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.category-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.category-card p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.article-count {
    color: var(--secondary-color);
    font-weight: bold;
    font-size: 0.9rem;
}

.recent-articles {
    padding: 80px 0;
    background: white;
}

.articles-list {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    margin-top: 3rem;
}

.article-item {
    display: flex;
    gap: 2rem;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 3px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.article-item:hover {
    transform: translateX(5px);
}

.article-thumbnail {
    width: 200px;
    height: 150px;
    background-size: cover;
    background-position: center;
    flex-shrink: 0;
}

.article-info {
    padding: 1.5rem;
    flex: 1;
}

.article-info h3 {
    font-size: 1.3rem;
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.article-info p {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.newsletter-section {
    padding: 80px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    text-align: center;
}

.newsletter-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.newsletter-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.newsletter-form {
    display: flex;
    gap: 1rem;
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form input {
    flex: 1;
    padding: 1rem;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
}

.newsletter-form button {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s ease;
}

.newsletter-form button:hover {
    background: #e55a2b;
}

/* FAQ Page Styles */
.faq-hero {
    background: linear-gradient(rgba(26, 54, 93, 0.8), rgba(45, 90, 135, 0.8)), url('/gallery/7.jpg');
    background-size: cover;
    background-position: center;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-top: 80px;
}

.faq-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.faq-hero p {
    font-size: 1.3rem;
    max-width: 600px;
    margin: 0 auto;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.faq-content {
    padding: 80px 0;
    background: white;
}

.faq-intro {
    text-align: center;
    margin-bottom: 4rem;
}

.faq-intro p {
    font-size: 1.1rem;
    color: var(--text-light);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.7;
}

.faq-category {
    margin-bottom: 3rem;
}

.category-title {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--secondary-color);
}

.category-title i {
    color: var(--secondary-color);
}

.faq-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.faq-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.faq-item.active {
    border-color: var(--primary-color);
    box-shadow: 0 5px 15px rgba(26, 54, 93, 0.1);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background 0.3s ease;
}

.faq-question:hover {
    background: var(--bg-light);
}

.faq-question h4 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin: 0;
    flex: 1;
}

.faq-question i {
    color: var(--primary-color);
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-item.active .faq-answer {
    max-height: 200px;
}

.faq-answer p {
    padding: 0 1.5rem 1.5rem;
    color: var(--text-light);
    line-height: 1.7;
    margin: 0;
}

.faq-contact {
    padding: 80px 0;
    background: var(--bg-light);
}

.contact-box {
    background: white;
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    max-width: 600px;
    margin: 0 auto;
}

.contact-box h2 {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.contact-box p {
    color: var(--text-light);
    font-size: 1.1rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Hotels Page Styles */
.hotels-hero {
    background: linear-gradient(rgba(26, 54, 93, 0.8), rgba(45, 90, 135, 0.8)), url('/gallery/1.jpg');
    background-size: cover;
    background-position: center;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    margin-top: 80px;
}

.hotels-hero h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hotels-hero p {
    font-size: 1.3rem;
    max-width: 600px;
    margin: 0 auto;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.hotels-by-location {
    padding: 80px 0;
}

.hotels-by-location:nth-child(even) {
    background: var(--bg-light);
}

.location-header {
    text-align: center;
    margin-bottom: 3rem;
}

.location-description {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-top: 1rem;
}

.hotels-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.hotel-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hotel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.hotel-image {
    height: 250px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.hotel-category {
    position: absolute;
    top: 15px;
    right: 15px;
    background: var(--secondary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
}

.hotel-rating {
    position: absolute;
    bottom: 15px;
    right: 15px;
    display: flex;
    gap: 0.2rem;
}

.hotel-rating i {
    color: #ffd700;
    font-size: 1.2rem;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
}

.hotel-content {
    padding: 2rem;
}

.hotel-content h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.hotel-description {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.hotel-features {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.feature-tag {
    background: var(--bg-light);
    color: var(--text-dark);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.feature-tag i {
    color: var(--success-color);
    font-size: 0.8rem;
}

.hotel-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.hotel-price {
    display: flex;
    flex-direction: column;
}

.hotel-price .price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--secondary-color);
}

.hotel-price .per-night {
    font-size: 0.9rem;
    color: var(--text-light);
}

.book-button {
    background: var(--success-color);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.book-button:hover {
    background: #059669;
    transform: translateY(-2px);
}

.hotel-categories {
    padding: 80px 0;
    background: white;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.tip-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.tip-card:hover {
    transform: translateY(-5px);
}

.tip-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
}

.tip-card h3 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.tip-card p {
    color: var(--text-light);
    line-height: 1.6;
}

.price-range {
    color: var(--secondary-color);
    font-weight: bold;
    font-size: 1.1rem;
    margin-top: 1rem;
}

.booking-tips {
    padding: 80px 0;
    background: var(--bg-light);
}

.hotels-contact {
    padding: 80px 0;
    background: white;
}
