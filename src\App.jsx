import { Routes, Route } from 'react-router-dom'
import Header from './components/Header'
import Footer from './components/Footer'
import WhatsAppButton from './components/WhatsAppButton'
import Home from './pages/Home'
import Cars from './pages/Cars'
import Hotels from './pages/Hotels'
import Magazine from './pages/Magazine'
import Contact from './pages/Contact'
import FAQ from './pages/FAQ'
import SigiryaArticle from './pages/SigiryaArticle'

function App() {
  return (
    <div className="App">
      <Header />
      <main>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/cars" element={<Cars />} />
          <Route path="/hotels" element={<Hotels />} />
          <Route path="/magazine" element={<Magazine />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/faq" element={<FAQ />} />
          <Route path="/article-sigiriya-guide" element={<SigiryaArticle />} />
        </Routes>
      </main>
      <Footer />
      <WhatsAppButton />
    </div>
  )
}

export default App
