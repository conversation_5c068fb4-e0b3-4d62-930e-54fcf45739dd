import { Helmet } from 'react-helmet-async'
import { useState } from 'react'

const Gallery = () => {
  const [selectedImage, setSelectedImage] = useState(null)

  // List of all gallery images
  const galleryImages = [
    '/gallery/1.jpg',
    '/gallery/2.jpg',
    '/gallery/3.jpg',
    '/gallery/4.jpg',
    '/gallery/5.jpg',
    '/gallery/6.jpg',
    '/gallery/7.jpg',
    '/gallery/8.jpg',
    '/gallery/9.jpg',
    '/gallery/10.jpg',
    '/gallery/11.jpg',
    '/gallery/12.jpg',
    '/gallery/13.jpg',
    '/gallery/14.jpg',
    '/gallery/beach.jpg',
    '/gallery/car-1.jpg',
    '/gallery/car-2.jpg',
    '/gallery/car1.jpg',
    '/gallery/car2.jpg',
    '/gallery/car3.jpg',
    '/gallery/ella.jpg',
    '/gallery/galle.jpg',
    '/gallery/hero-bg.jpg',
    '/gallery/kandy.jpg',
    '/gallery/our-car-1.jpg',
    '/gallery/our-car-2.jpg',
    '/gallery/safari.jpg',
    '/gallery/sigiriya.jpg',
    '/gallery/view/1.jpg',
    '/gallery/view/2.jpg',
    '/gallery/view/3.jpg',
    '/gallery/view/4.jpg',
    '/gallery/view/5.jpg',
    '/gallery/view/6.jpg',
    '/gallery/view/7.jpg',
    '/gallery/view/8.jpg',
    '/gallery/view/9.jpg',
    '/gallery/view/10.jpg',
    '/gallery/view/11.jpg',
    '/gallery/view/12.jpg',
    '/gallery/view/13.jpg',
    '/gallery/view/14.jpg',
    '/gallery/view/15.jpg',
    '/gallery/view/16.jpg',
    '/gallery/view/17.jpg',
    '/gallery/view/18.jpg',
    '/gallery/view/19.jpg'
  ]

  const openLightbox = (image) => {
    setSelectedImage(image)
  }

  const closeLightbox = () => {
    setSelectedImage(null)
  }

  const nextImage = () => {
    const currentIndex = galleryImages.indexOf(selectedImage)
    const nextIndex = (currentIndex + 1) % galleryImages.length
    setSelectedImage(galleryImages[nextIndex])
  }

  const prevImage = () => {
    const currentIndex = galleryImages.indexOf(selectedImage)
    const prevIndex = currentIndex === 0 ? galleryImages.length - 1 : currentIndex - 1
    setSelectedImage(galleryImages[prevIndex])
  }

  return (
    <>
      <Helmet>
        <title>גלריה - סרי ווי | תמונות מסרי לנקה</title>
        <meta name="description" content="גלריית תמונות מסרי לנקה - נופים, אטרקציות, רכבים ורגעים מיוחדים מהטיולים שלנו" />
        <meta name="keywords" content="גלריה, תמונות, סרי לנקה, נופים, אטרקציות, טיולים" />
      </Helmet>

      {/* Gallery Grid */}
      <section className="gallery-section">
        <h1 style={{textAlign: 'center', padding: '2rem'}}>גלריה - Gallery Test</h1>
        <div className="gallery-grid">
          {galleryImages.map((image, index) => (
            <div 
              key={index} 
              className="gallery-item"
              onClick={() => openLightbox(image)}
            >
              <img 
                src={image} 
                alt={`Gallery image ${index + 1}`}
                loading="lazy"
              />
              <div className="gallery-overlay">
                <i className="fas fa-expand"></i>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Lightbox */}
      {selectedImage && (
        <div className="lightbox" onClick={closeLightbox}>
          <div className="lightbox-content" onClick={(e) => e.stopPropagation()}>
            <button className="lightbox-close" onClick={closeLightbox}>
              <i className="fas fa-times"></i>
            </button>
            <button className="lightbox-prev" onClick={prevImage}>
              <i className="fas fa-chevron-left"></i>
            </button>
            <img src={selectedImage} alt="Gallery" />
            <button className="lightbox-next" onClick={nextImage}>
              <i className="fas fa-chevron-right"></i>
            </button>
          </div>
        </div>
      )}
    </>
  )
}

export default Gallery
