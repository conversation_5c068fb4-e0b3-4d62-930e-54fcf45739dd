# Deployment Guide - Sri Way Website

## 🚀 Deploy to Railway.com

### Step 1: Prepare Your Repository

1. **Initialize Git** (if not already done):
   ```bash
   git init
   git add .
   git commit -m "Initial commit - Sri Way React website"
   ```

2. **Create GitHub Repository**:
   - Go to [GitHub.com](https://github.com)
   - Click "New Repository"
   - Name it `sriway-website` (or your preferred name)
   - Don't initialize with README (we already have files)
   - Click "Create Repository"

3. **Push to GitHub**:
   ```bash
   git branch -M main
   git remote add origin https://github.com/YOUR_USERNAME/sriway-website.git
   git push -u origin main
   ```

### Step 2: Deploy on Railway

1. **Go to Railway**:
   - Visit [Railway.app](https://railway.app)
   - Sign up/Login with GitHub

2. **Create New Project**:
   - Click "New Project"
   - Select "Deploy from GitHub repo"
   - Choose your `sriway-website` repository

3. **Automatic Deployment**:
   - Railway will automatically detect it's a Node.js project
   - It will read the `railway.json` configuration
   - Build process will run automatically
   - Your site will be deployed!

4. **Get Your URL**:
   - Railway will provide a URL like: `https://your-app-name.railway.app`
   - You can also add a custom domain later

### Step 3: Verify Deployment

1. **Check Build Logs**:
   - In Railway dashboard, check the deployment logs
   - Ensure build completed successfully

2. **Test Your Site**:
   - Visit your Railway URL
   - Test all pages and navigation
   - Verify images load correctly
   - Test contact forms

### Step 4: Custom Domain (Optional)

1. **In Railway Dashboard**:
   - Go to your project settings
   - Click "Domains"
   - Add your custom domain
   - Follow DNS configuration instructions

## 🔧 Configuration Files

### railway.json
```json
{
  "$schema": "https://railway.app/railway.schema.json",
  "build": {
    "builder": "NIXPACKS"
  },
  "deploy": {
    "startCommand": "npm run start",
    "restartPolicyType": "ON_FAILURE",
    "restartPolicyMaxRetries": 10
  }
}
```

### package.json Scripts
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "start": "vite preview --port $PORT --host 0.0.0.0"
  }
}
```

## 🌐 Environment Variables

No environment variables are required for basic deployment. The site is fully static after build.

## 📱 Testing

### Local Testing
```bash
# Development
npm run dev

# Production build test
npm run build
npm run preview
```

### Production Testing
- Test all pages load correctly
- Verify responsive design on mobile
- Check contact form functionality
- Test WhatsApp links
- Verify SEO meta tags

## 🔄 Updates

To update your deployed site:

1. **Make Changes Locally**
2. **Commit and Push**:
   ```bash
   git add .
   git commit -m "Update: description of changes"
   git push
   ```
3. **Automatic Deployment**: Railway will automatically redeploy

## 🆘 Troubleshooting

### Build Fails
- Check build logs in Railway dashboard
- Ensure all dependencies are in package.json
- Verify Node.js version compatibility

### Site Not Loading
- Check if build completed successfully
- Verify start command in railway.json
- Check for any console errors

### Images Not Loading
- Ensure images are in `public/gallery/` folder
- Check image paths start with `/gallery/`
- Verify images were committed to git

## 📞 Support

If you need help with deployment:
- Check Railway documentation
- Contact Railway support
- Review build logs for specific errors

---

*Your Sri Way website is now ready for the world! 🌍*
