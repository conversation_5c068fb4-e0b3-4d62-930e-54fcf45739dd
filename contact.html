<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>צור קשר - סרי ווי | נהגים ומדריכים פרטיים בסרי לנקה</title>
    <meta name="description" content="צרו קשר עם סרי ווי - הנהגים והמדריכים הפרטיים המובילים בסרי לנקה. זמינים 24/7 לייעוץ ותכנון הטיול המושלם שלכם">
    <meta name="keywords" content="צור קשר, סרי לנקה, נהגים פרטיים, מדריכים, טיולים, ייעוץ טיולים, תכנון מסלול">
    <meta name="author" content="סרי ווי - מדריכי טיולים מקצועיים">
    <meta property="og:title" content="צור קשר - סרי ווי">
    <meta property="og:description" content="צרו קשר איתנו לתכנון הטיול המושלם בסרי לנקה">
    <meta property="og:image" content="gallery/contact-hero.jpg">
    <meta property="og:url" content="https://sriway.com/contact.html">
    <meta property="og:type" content="website">
    <link rel="canonical" href="https://sriway.com/contact.html">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <style>
        /* Contact specific styles */
        :root {
            --primary-color: #1a365d;
            --secondary-color: #ff6b35;
            --accent-color: #2d5a87;
            --text-dark: #333;
            --text-light: #666;
            --bg-light: #f8f9fa;
            --success-color: #10b981;
            --error-color: #ef4444;
        }

        .contact-hero {
            background: linear-gradient(rgba(26, 54, 93, 0.8), rgba(45, 90, 135, 0.8)), url('gallery/1.jpg');
            background-size: cover;
            background-position: center;
            height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            margin-top: 80px;
        }

        .contact-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }

        .contact-hero p {
            font-size: 1.3rem;
            max-width: 600px;
            margin: 0 auto;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        /* Contact Methods */
        .contact-methods {
            padding: 80px 0;
            background: white;
        }

        .methods-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .method-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s, box-shadow 0.3s;
            border-top: 4px solid var(--secondary-color);
        }

        .method-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.15);
        }

        .method-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 1.5rem;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
        }

        .method-card h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }

        .method-card p {
            color: var(--text-light);
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .contact-link {
            background: var(--secondary-color);
            color: white;
            padding: 1rem 2rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s;
            display: inline-block;
        }

        .contact-link:hover {
            background: #e55a2b;
            transform: translateY(-2px);
        }

        .whatsapp-link {
            background: #25d366;
        }

        .whatsapp-link:hover {
            background: #20b954;
        }

        /* Contact Form */
        .contact-form-section {
            padding: 80px 0;
            background: var(--bg-light);
        }

        .form-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.3s;
            font-family: inherit;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .submit-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 1rem 3rem;
            border: none;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* Success/Error Messages */
        .message {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            display: none;
        }

        .message.success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            border: 1px solid var(--success-color);
        }

        .message.error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-color);
            border: 1px solid var(--error-color);
        }

        /* FAQ Section */
        .faq-section {
            padding: 80px 0;
            background: white;
        }

        .faq-grid {
            display: grid;
            gap: 1.5rem;
            margin-top: 3rem;
        }

        .faq-item {
            background: var(--bg-light);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 3px 15px rgba(0,0,0,0.05);
        }

        .faq-question {
            background: var(--primary-color);
            color: white;
            padding: 1.5rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s;
        }

        .faq-question:hover {
            background: var(--accent-color);
        }

        .faq-question h4 {
            margin: 0;
            font-size: 1.2rem;
        }

        .faq-toggle {
            font-size: 1.5rem;
            transition: transform 0.3s;
        }

        .faq-answer {
            padding: 0 1.5rem;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-answer.active {
            padding: 1.5rem;
            max-height: 200px;
        }

        .faq-answer p {
            color: var(--text-light);
            line-height: 1.6;
            margin: 0;
        }

        /* Location Info */
        .location-section {
            padding: 80px 0;
            background: var(--bg-light);
        }

        .location-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .location-card {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .location-card h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .location-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            color: var(--text-light);
        }

        .location-info i {
            color: var(--secondary-color);
            width: 20px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .contact-hero h1 {
                font-size: 2rem;
            }
            
            .contact-hero p {
                font-size: 1.1rem;
            }
            
            .form-container {
                padding: 2rem 1rem;
                margin: 0 1rem;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .methods-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <nav>
            <div class="logo">סרי ווי</div>
            <ul class="nav-links">
                <li><a href="index.html">בית</a></li>
                <li><a href="cars.html">הרכבים שלנו</a></li>
                <li><a href="hotels.html">מלונות</a></li>
                <li><a href="magazine.html">מגזין</a></li>
                <li><a href="faq.html">שאלות נפוצות</a></li>
                <li><a href="contact.html" class="active">צור קשר</a></li>
            </ul>
            <button class="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="contact-hero">
        <div class="hero-content">
            <h1>צור קשר איתנו</h1>
            <p>אנחנו כאן בשבילכם 24/7 לכל שאלה, ייעוץ או תכנון הטיול המושלם שלכם בסרי לנקה</p>
        </div>
    </section>

    <!-- Contact Methods -->
    <section class="contact-methods">
        <div class="container">
            <h2 class="section-title">דרכי התקשרות</h2>
            <p style="text-align: center; margin-bottom: 3rem; color: var(--text-light); font-size: 1.1rem;">בחרו את הדרך הנוחה לכם ביותר ליצירת קשר</p>

            <div class="methods-grid">
                <div class="method-card">
                    <div class="method-icon">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <h3>וואטסאפ</h3>
                    <p>הדרך הכי מהירה ונוחה ליצירת קשר. זמינים 24/7 לכל שאלה ותיאום</p>
                    <a href="https://wa.me/94771234567" class="contact-link whatsapp-link">
                        <i class="fab fa-whatsapp"></i> שלח הודעה
                    </a>
                </div>

                <div class="method-card">
                    <div class="method-icon">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h3>טלפון</h3>
                    <p>התקשרו אלינו ישירות לייעוץ מקצועי ותכנון מסלול מותאם אישית</p>
                    <a href="tel:+94771234567" class="contact-link">
                        <i class="fas fa-phone"></i> +94 77 123 4567
                    </a>
                </div>

                <div class="method-card">
                    <div class="method-icon">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h3>אימייל</h3>
                    <p>שלחו לנו פרטים על הטיול שלכם ונחזור אליכם עם הצעה מפורטת</p>
                    <a href="mailto:<EMAIL>" class="contact-link">
                        <i class="fas fa-envelope"></i> <EMAIL>
                    </a>
                </div>

                <div class="method-card">
                    <div class="method-icon">
                        <i class="fab fa-facebook-messenger"></i>
                    </div>
                    <h3>פייסבוק</h3>
                    <p>עקבו אחרינו בפייסבוק לעדכונים ותמונות מהטיולים שלנו</p>
                    <a href="#" class="contact-link">
                        <i class="fab fa-facebook"></i> עמוד הפייסבוק
                    </a>
                </div>

                <div class="method-card">
                    <div class="method-icon">
                        <i class="fab fa-instagram"></i>
                    </div>
                    <h3>אינסטגרם</h3>
                    <p>תמונות מדהימות מסרי לנקה וסיפורים מהטיולים שלנו</p>
                    <a href="#" class="contact-link">
                        <i class="fab fa-instagram"></i> @sriway_israel
                    </a>
                </div>

                <div class="method-card">
                    <div class="method-icon">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <h3>תיאום פגישה</h3>
                    <p>קבעו פגישת ייעוץ אישית (זום או טלפון) לתכנון מפורט של הטיול</p>
                    <a href="#contact-form" class="contact-link">
                        <i class="fas fa-calendar"></i> קבע פגישה
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section class="contact-form-section" id="contact-form">
        <div class="container">
            <h2 class="section-title">שלחו לנו הודעה</h2>
            <p style="text-align: center; margin-bottom: 3rem; color: var(--text-light); font-size: 1.1rem;">מלאו את הפרטים ונחזור אליכם בהקדם עם הצעה מותאמת אישית</p>

            <div class="form-container">
                <div id="form-message" class="message"></div>

                <form id="contact-form-element">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="firstName">שם פרטי *</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">שם משפחה *</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="email">אימייל *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="phone">טלפון *</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label for="travelDates">תאריכי הטיול</label>
                            <input type="text" id="travelDates" name="travelDates" placeholder="למשל: 15-25 במרץ 2025">
                        </div>
                        <div class="form-group">
                            <label for="groupSize">מספר נוסעים</label>
                            <select id="groupSize" name="groupSize">
                                <option value="">בחרו מספר נוסעים</option>
                                <option value="1">1 נוסע</option>
                                <option value="2">2 נוסעים</option>
                                <option value="3-4">3-4 נוסעים</option>
                                <option value="5-6">5-6 נוסעים</option>
                                <option value="7+">7+ נוסעים</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="serviceType">סוג השירות המבוקש</label>
                        <select id="serviceType" name="serviceType">
                            <option value="">בחרו סוג שירות</option>
                            <option value="driver">נהג פרטי</option>
                            <option value="guide">מדריך פרטי</option>
                            <option value="both">נהג + מדריך</option>
                            <option value="consultation">ייעוץ ותכנון מסלול</option>
                            <option value="other">אחר</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="budget">תקציב משוער (אופציונלי)</label>
                        <select id="budget" name="budget">
                            <option value="">בחרו טווח תקציב</option>
                            <option value="budget">תקציבי (עד $50 ליום)</option>
                            <option value="mid-range">בינוני ($50-100 ליום)</option>
                            <option value="luxury">יוקרתי ($100+ ליום)</option>
                            <option value="flexible">גמיש</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="message">פרטים נוספים על הטיול *</label>
                        <textarea id="message" name="message" placeholder="ספרו לנו על הטיול שלכם - אילו מקומות אתם רוצים לבקר, מה מעניין אתכם, האם יש צרכים מיוחדים וכל מידע נוסף שיעזור לנו לתכנן עבורכם את הטיול המושלם" required></textarea>
                    </div>

                    <button type="submit" class="submit-btn" id="submit-btn">
                        <i class="fas fa-paper-plane"></i> שלח הודעה
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq-section">
        <div class="container">
            <h2 class="section-title">שאלות נפוצות</h2>
            <p style="text-align: center; margin-bottom: 3rem; color: var(--text-light); font-size: 1.1rem;">תשובות לשאלות הכי נפוצות שאנחנו מקבלים</p>

            <div class="faq-grid">
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>כמה זמן לפני הטיול כדאי ליצור קשר?</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>מומלץ ליצור קשר לפחות 2-4 שבועות לפני הטיול, במיוחד בעונות השיא (דצמבר-מרץ ויולי-אוגוסט). זה יאפשר לנו לתכנן עבורכם את המסלול הטוב ביותר ולהבטיח זמינות.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>האם אתם מספקים שירותים בכל רחבי סרי לנקה?</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>כן! אנחנו מספקים שירותי נהגים ומדריכים בכל רחבי סרי לנקה - מקולומבו ועד לחופי הדרום, מההרים המרכזיים ועד לחוף המזרחי. יש לנו צוות מקצועי בכל האזורים.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>מה כלול במחיר השירות?</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>המחיר כולל: נהג מקצועי דובר עברית/אנגלית, רכב מוזג ונוח, דלק, חניות ואגרות דרכים. לא כלול: לינה, אוכל, כניסות לאטרקציות ופעילויות נוספות.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>האם אפשר לשנות את המסלול במהלך הטיול?</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>בהחלט! אנחנו מבינים שלפעמים רוצים לשנות תוכניות. הנהגים שלנו גמישים ויעזרו לכם להתאים את המסלול לפי הרצונות שלכם, בכפוף לזמינות ולתנאי הדרכים.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>איך מתבצע התשלום?</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>אנחנו מקבלים תשלום במזומן (דולרים או רופי), העברה בנקאית או PayPal. בדרך כלל מבקשים מקדמה של 30% להזמנה ויתרת התשלום בתחילת הטיול.</p>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <h4>מה קורה במקרה של ביטול?</h4>
                        <span class="faq-toggle">+</span>
                    </div>
                    <div class="faq-answer">
                        <p>ביטול עד 7 ימים לפני הטיול - החזר מלא של המקדמה. ביטול 3-7 ימים לפני - החזר של 50%. ביטול פחות מ-3 ימים - ללא החזר. במקרים חריגים (מחלה, אסון טבע) נבחן כל מקרה לגופו.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Location Section -->
    <section class="location-section">
        <div class="container">
            <h2 class="section-title">איפה אנחנו נמצאים</h2>
            <p style="text-align: center; margin-bottom: 3rem; color: var(--text-light); font-size: 1.1rem;">הצוות שלנו פרוס בכל רחבי סרי לנקה כדי לתת לכם את השירות הטוב ביותר</p>

            <div class="location-grid">
                <div class="location-card">
                    <h4>🏢 המשרד הראשי - קולומבו</h4>
                    <div class="location-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>רובע קולומבו 3, ליד גאלה פייס גרין</span>
                    </div>
                    <div class="location-info">
                        <i class="fas fa-clock"></i>
                        <span>פתוח 24/7 (תמיכה טלפונית)</span>
                    </div>
                    <div class="location-info">
                        <i class="fas fa-phone"></i>
                        <span>+94 77 123 4567</span>
                    </div>
                </div>

                <div class="location-card">
                    <h4>✈️ שירות איסוף משדה התעופה</h4>
                    <div class="location-info">
                        <i class="fas fa-plane"></i>
                        <span>נמל התעופה הבינלאומי בנדרנאיקה (קולומבו)</span>
                    </div>
                    <div class="location-info">
                        <i class="fas fa-clock"></i>
                        <span>זמין 24/7 - כל הטיסות</span>
                    </div>
                    <div class="location-info">
                        <i class="fas fa-info-circle"></i>
                        <span>שירות איסוף חינם עם הזמנת נהג</span>
                    </div>
                </div>

                <div class="location-card">
                    <h4>🌴 נציגויות באזורים תיירותיים</h4>
                    <div class="location-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>קנדי, גאלה, אלה, נוארה אליה</span>
                    </div>
                    <div class="location-info">
                        <i class="fas fa-users"></i>
                        <span>נהגים ומדריכים מקומיים</span>
                    </div>
                    <div class="location-info">
                        <i class="fas fa-headset"></i>
                        <span>תמיכה מקומית בכל אזור</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>סרי ווי</h3>
                    <p>המדריכים והנהגים הפרטיים המובילים בסרי לנקה לקהילה הישראלית. אנחנו כאן כדי להפוך את החלום שלכם למציאות.</p>
                </div>
                <div class="footer-section">
                    <h3>דרכי התקשרות</h3>
                    <p><i class="fas fa-phone"></i> +94 77 123 4567</p>
                    <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    <p><i class="fab fa-whatsapp"></i> וואטסאפ 24/7</p>
                </div>
                <div class="footer-section">
                    <h3>קישורים מהירים</h3>
                    <p><a href="cars.html">הרכבים שלנו</a></p>
                    <p><a href="hotels.html">מלונות</a></p>
                    <p><a href="magazine.html">מגזין</a></p>
                    <p><a href="faq.html">שאלות נפוצות</a></p>
                </div>
                <div class="footer-section">
                    <h3>עקבו אחרינו</h3>
                    <p><a href="#"><i class="fab fa-facebook"></i> פייסבוק</a></p>
                    <p><a href="#"><i class="fab fa-instagram"></i> אינסטגרם</a></p>
                    <p><a href="#"><i class="fab fa-youtube"></i> יוטיוב</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 סרי ווי. כל הזכויות שמורות.</p>
            </div>
        </div>
    </footer>

    <!-- WhatsApp Button -->
    <a href="https://wa.me/94771234567" class="whatsapp-btn" aria-label="צור קשר בוואטסאפ">
        <i class="fab fa-whatsapp"></i>
    </a>

    <!-- JavaScript -->
    <script src="script.js"></script>
    <script>
        // FAQ Toggle Functionality
        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const toggle = element.querySelector('.faq-toggle');

            // Close all other FAQ items
            document.querySelectorAll('.faq-answer').forEach(item => {
                if (item !== answer) {
                    item.classList.remove('active');
                    item.previousElementSibling.querySelector('.faq-toggle').textContent = '+';
                }
            });

            // Toggle current FAQ item
            answer.classList.toggle('active');
            toggle.textContent = answer.classList.contains('active') ? '−' : '+';
        }

        // Contact Form Submission
        document.getElementById('contact-form-element').addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submit-btn');
            const messageDiv = document.getElementById('form-message');

            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> שולח...';

            // Get form data
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);

            // Simulate form submission (replace with actual form handling)
            setTimeout(() => {
                // Show success message
                messageDiv.className = 'message success';
                messageDiv.style.display = 'block';
                messageDiv.innerHTML = '<i class="fas fa-check-circle"></i> תודה! ההודעה נשלחה בהצלחה. נחזור אליכם בהקדם.';

                // Reset form
                this.reset();

                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> שלח הודעה';

                // Scroll to message
                messageDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });

                // Hide message after 5 seconds
                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 5000);

            }, 2000); // Simulate 2 second delay
        });

        // Form validation
        document.querySelectorAll('input[required], textarea[required]').forEach(field => {
            field.addEventListener('blur', function() {
                if (!this.value.trim()) {
                    this.style.borderColor = 'var(--error-color)';
                } else {
                    this.style.borderColor = 'var(--success-color)';
                }
            });

            field.addEventListener('input', function() {
                if (this.value.trim()) {
                    this.style.borderColor = 'var(--success-color)';
                }
            });
        });

        // Email validation
        document.getElementById('email').addEventListener('blur', function() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (this.value && !emailRegex.test(this.value)) {
                this.style.borderColor = 'var(--error-color)';
            } else if (this.value) {
                this.style.borderColor = 'var(--success-color)';
            }
        });

        // Phone validation
        document.getElementById('phone').addEventListener('blur', function() {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            if (this.value && !phoneRegex.test(this.value)) {
                this.style.borderColor = 'var(--error-color)';
            } else if (this.value) {
                this.style.borderColor = 'var(--success-color)';
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
